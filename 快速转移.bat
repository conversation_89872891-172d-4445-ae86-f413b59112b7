@echo off
chcp 65001 >nul
title Minecraft 玩家数据转移工具

echo.
echo ╔══════════════════════════════════════╗
echo ║     Minecraft 玩家数据转移工具       ║
echo ╚══════════════════════════════════════╝
echo.

echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python
    echo.
    echo 请先安装Python：
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装最新版本的Python
    echo 3. 安装时勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo ✓ Python环境正常
echo.

echo 正在启动转移工具...
python simple_transfer.py

if %errorlevel% neq 0 (
    echo.
    echo 如果遇到NBT库相关错误，请运行以下命令安装：
    echo pip install nbtlib
    echo 或者
    echo pip install anvil-parser
    echo.
)

pause
