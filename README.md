# Minecraft 玩家数据转移脚本

这个脚本用于解决Minecraft单人模式存档移交时的玩家数据问题。

## 问题背景

在Minecraft中：
- **单人模式**：使用`level.dat`文件中的玩家数据
- **多人模式**：使用`playerdata`文件夹中对应UUID的`.dat`文件

当您要将多人存档移交给其他人作为单人存档使用时，需要将指定玩家的数据从`playerdata`文件夹复制到`level.dat`中。

## 使用方法

### 方法一：简单使用（推荐）

1. **直接运行**：双击 `快速转移.bat` 文件
2. **选择玩家**：从列表中选择要转移的玩家
3. **完成转移**：脚本会自动处理所有步骤

### 方法二：命令行使用

#### 1. 安装依赖（可选，推荐）

```bash
# 安装NBT处理库（推荐）
pip install nbtlib
# 或者
pip install anvil-parser
```

#### 2. 使用简化版脚本

```bash
python simple_transfer.py
```

#### 3. 使用完整版脚本

```bash
# 查看可用玩家
python transfer_playerdata.py

# 转移指定玩家数据
python transfer_playerdata.py <UUID> [存档路径]
```

**参数说明：**
- `<UUID>`：要转移的玩家UUID（必需）
- `[存档路径]`：存档文件夹路径（可选，默认为当前目录）

**示例：**
```bash
# 在当前目录转移玩家数据
python transfer_playerdata.py 07ae91b0-3077-420b-bb8c-f29041d05cd9

# 指定存档路径
python transfer_playerdata.py 07ae91b0-3077-420b-bb8c-f29041d05cd9 "C:\minecraft\saves\MyWorld"
```

## 当前存档中的玩家

根据您的存档，以下是可用的玩家UUID：
- `07ae91b0-3077-420b-bb8c-f29041d05cd9`
- `4181f174-88b9-453d-980f-686f3d521f16`
- `749126bc-4467-41b4-be12-d24f4496cfad`
- `90a2ba02-6174-4ed2-a3cc-11adc6a0ba4c`
- `ae51ed3d-e647-4719-9d3d-f2284d3c556d`
- `b9947d90-4005-4d4e-b425-9fa5513b5d75`
- `ececee07-d1c5-45b5-8981-2cd403614dfe`
- `f6e77564-05e1-424b-8803-8f124f7f588d`
- `ff75ac3f-d688-4d0a-8d5d-6a29e3545dd8`

## 安全特性

- **自动备份**：脚本会在修改`level.dat`之前自动创建备份文件（`level.dat.backup`）
- **文件验证**：检查所需文件是否存在
- **错误处理**：提供详细的错误信息和处理建议

## 注意事项

1. **备份重要**：虽然脚本会自动备份，但建议在操作前手动备份整个存档
2. **关闭游戏**：在运行脚本前请确保Minecraft已完全关闭
3. **权限问题**：确保脚本有读写存档文件的权限
4. **版本兼容**：脚本支持Minecraft 1.16.5及相近版本

## 故障排除

### 常见错误

1. **找不到文件**
   - 确保UUID正确
   - 确保存档路径正确
   - 检查文件权限

2. **NBT库安装失败**
   - 尝试使用管理员权限运行命令提示符
   - 确保Python和pip已正确安装
   - 尝试不同的NBT库：`pip install anvil-parser`

3. **权限被拒绝**
   - 关闭Minecraft和相关程序
   - 以管理员身份运行脚本
   - 检查文件是否被其他程序占用

### 恢复备份

如果出现问题，可以从备份恢复：
```bash
copy level.dat.backup level.dat
```

## 文件说明

- `快速转移.bat` - 一键运行脚本（推荐使用）
- `simple_transfer.py` - 简化版转移脚本，带交互界面
- `transfer_playerdata.py` - 完整版转移脚本，支持命令行参数
- `install_dependencies.bat` - 依赖安装脚本
- `run_transfer.bat` - 完整版脚本的运行器
- `README.md` - 使用说明文档

## 技术细节

脚本支持多种NBT处理方式：
1. `nbtlib`：推荐使用，功能最完整
2. `anvil-parser`：备选方案，兼容性好
3. 手动方法：最后手段，不推荐使用

脚本会自动尝试使用最佳可用方法，无需手动选择。
