#!/usr/bin/env python3
"""
测试脚本 - 验证转移工具是否正常工作
"""

import os
import sys

def test_environment():
    """测试环境检查"""
    print("=== 环境测试 ===")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 检查必要文件
    required_files = ['level.dat', 'playerdata']
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ 找到: {file}")
        else:
            print(f"✗ 缺失: {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n警告：缺少必要文件: {missing_files}")
        print("请确保在Minecraft存档目录中运行此脚本")
        return False
    
    return True

def test_nbt_libraries():
    """测试NBT库"""
    print("\n=== NBT库测试 ===")
    
    libraries = [
        ('nbtlib', 'nbt'),
        ('anvil-parser', 'anvil')
    ]
    
    available_libs = []
    
    for lib_name, import_name in libraries:
        try:
            __import__(import_name)
            print(f"✓ {lib_name} 可用")
            available_libs.append(lib_name)
        except ImportError:
            print(f"✗ {lib_name} 未安装")
    
    if not available_libs:
        print("\n建议安装NBT处理库:")
        print("pip install nbtlib")
        print("或")
        print("pip install anvil-parser")
        return False
    
    return True

def test_playerdata():
    """测试玩家数据"""
    print("\n=== 玩家数据测试 ===")
    
    playerdata_dir = "playerdata"
    if not os.path.exists(playerdata_dir):
        print("✗ playerdata目录不存在")
        return False
    
    players = []
    for file in os.listdir(playerdata_dir):
        if file.endswith('.dat') and not file.endswith('.dat_old'):
            uuid = file[:-4]
            players.append(uuid)
            print(f"✓ 找到玩家: {uuid}")
    
    if not players:
        print("✗ 未找到玩家数据文件")
        return False
    
    print(f"\n总共找到 {len(players)} 个玩家")
    return True

def test_level_dat():
    """测试level.dat"""
    print("\n=== level.dat测试 ===")
    
    level_dat = "level.dat"
    if not os.path.exists(level_dat):
        print("✗ level.dat不存在")
        return False
    
    # 检查文件大小
    size = os.path.getsize(level_dat)
    print(f"✓ level.dat存在，大小: {size} 字节")
    
    if size < 100:
        print("⚠ level.dat文件可能损坏（文件太小）")
        return False
    
    return True

def test_backup_functionality():
    """测试备份功能"""
    print("\n=== 备份功能测试 ===")
    
    import shutil
    
    test_file = "test_backup.txt"
    backup_file = f"{test_file}.backup"
    
    try:
        # 创建测试文件
        with open(test_file, 'w') as f:
            f.write("测试内容")
        
        # 创建备份
        shutil.copy2(test_file, backup_file)
        
        # 检查备份
        if os.path.exists(backup_file):
            print("✓ 备份功能正常")
            
            # 清理测试文件
            os.remove(test_file)
            os.remove(backup_file)
            return True
        else:
            print("✗ 备份创建失败")
            return False
            
    except Exception as e:
        print(f"✗ 备份测试失败: {e}")
        return False

def main():
    print("Minecraft 玩家数据转移工具 - 系统测试")
    print("=" * 50)
    
    tests = [
        ("环境检查", test_environment),
        ("NBT库检查", test_nbt_libraries),
        ("玩家数据检查", test_playerdata),
        ("level.dat检查", test_level_dat),
        ("备份功能检查", test_backup_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠ {test_name} 未通过")
        except Exception as e:
            print(f"✗ {test_name} 出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，工具可以正常使用！")
    elif passed >= total - 1:
        print("⚠ 大部分测试通过，工具应该可以使用")
    else:
        print("✗ 多个测试失败，请检查环境配置")
    
    print("\n建议:")
    if passed < total:
        print("- 确保在Minecraft存档目录中运行")
        print("- 安装NBT处理库: pip install nbtlib")
        print("- 检查文件权限")
    
    print("- 使用前请备份整个存档")
    print("- 关闭Minecraft后再运行工具")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
