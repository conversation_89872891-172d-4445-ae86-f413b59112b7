#!/usr/bin/env python3
"""
Minecraft存档玩家数据转移脚本
将指定UUID的playerdata数据载入level.dat中，用于单人模式存档移交

使用方法:
python transfer_playerdata.py <UUID> [存档路径]

例如:
python transfer_playerdata.py 07ae91b0-3077-420b-bb8c-f29041d05cd9
python transfer_playerdata.py 07ae91b0-3077-420b-bb8c-f29041d05cd9 /path/to/save/folder
"""

import os
import sys
import shutil
from pathlib import Path

try:
    from nbtlib import nbt
    print("使用nbtlib库处理NBT数据")
except ImportError:
    try:
        import anvil
        print("使用anvil库处理NBT数据")
    except ImportError:
        print("错误：需要安装NBT处理库")
        print("请运行以下命令之一：")
        print("pip install nbtlib")
        print("或者")
        print("pip install anvil-parser")
        sys.exit(1)

def backup_file(file_path):
    """创建文件备份"""
    backup_path = f"{file_path}.backup"
    shutil.copy2(file_path, backup_path)
    print(f"已创建备份: {backup_path}")
    return backup_path

def transfer_playerdata_nbtlib(uuid, save_path):
    """使用nbtlib库转移玩家数据"""
    playerdata_path = os.path.join(save_path, "playerdata", f"{uuid}.dat")
    level_dat_path = os.path.join(save_path, "level.dat")
    
    # 检查文件是否存在
    if not os.path.exists(playerdata_path):
        print(f"错误：找不到玩家数据文件: {playerdata_path}")
        return False
    
    if not os.path.exists(level_dat_path):
        print(f"错误：找不到level.dat文件: {level_dat_path}")
        return False
    
    try:
        # 读取玩家数据
        print(f"读取玩家数据: {playerdata_path}")
        player_data = nbt.load(playerdata_path)

        # 读取level.dat
        print(f"读取level.dat: {level_dat_path}")
        level_data = nbt.load(level_dat_path)

        # 备份level.dat
        backup_file(level_dat_path)

        # 将玩家数据复制到level.dat中
        print("正在转移玩家数据...")
        level_data['Data']['Player'] = player_data.copy()

        # 保存修改后的level.dat
        print(f"保存修改后的level.dat: {level_dat_path}")
        level_data.save(level_dat_path)

        print("✓ 玩家数据转移完成！")
        return True

    except Exception as e:
        print(f"错误：处理NBT数据时出现问题: {e}")
        return False

def transfer_playerdata_anvil(uuid, save_path):
    """使用anvil库转移玩家数据"""
    playerdata_path = os.path.join(save_path, "playerdata", f"{uuid}.dat")
    level_dat_path = os.path.join(save_path, "level.dat")
    
    # 检查文件是否存在
    if not os.path.exists(playerdata_path):
        print(f"错误：找不到玩家数据文件: {playerdata_path}")
        return False
    
    if not os.path.exists(level_dat_path):
        print(f"错误：找不到level.dat文件: {level_dat_path}")
        return False
    
    try:
        # 读取玩家数据
        print(f"读取玩家数据: {playerdata_path}")
        player_data = anvil.NBTFile.load(playerdata_path)
        
        # 读取level.dat
        print(f"读取level.dat: {level_dat_path}")
        level_data = anvil.NBTFile.load(level_dat_path)
        
        # 备份level.dat
        backup_file(level_dat_path)
        
        # 将玩家数据复制到level.dat中
        print("正在转移玩家数据...")
        level_data['Data']['Player'] = player_data.copy()
        
        # 保存修改后的level.dat
        print(f"保存修改后的level.dat: {level_dat_path}")
        level_data.save(level_dat_path)
        
        print("✓ 玩家数据转移完成！")
        return True
        
    except Exception as e:
        print(f"错误：处理NBT数据时出现问题: {e}")
        return False

def get_player_name_from_nbt(player_data):
    """从NBT数据中提取玩家名称"""
    try:
        # 尝试不同的可能字段
        if hasattr(player_data, 'get'):
            # nbtlib格式
            if 'bukkit' in player_data and 'lastKnownName' in player_data['bukkit']:
                return player_data['bukkit']['lastKnownName']
            elif 'Paper' in player_data and 'LastKnownName' in player_data['Paper']:
                return player_data['Paper']['LastKnownName']
        else:
            # anvil格式
            if 'bukkit' in player_data and 'lastKnownName' in player_data['bukkit']:
                return str(player_data['bukkit']['lastKnownName'])
            elif 'Paper' in player_data and 'LastKnownName' in player_data['Paper']:
                return str(player_data['Paper']['LastKnownName'])
    except:
        pass
    return None

def list_available_players(save_path):
    """列出可用的玩家UUID和名称"""
    playerdata_dir = os.path.join(save_path, "playerdata")
    if not os.path.exists(playerdata_dir):
        print("错误：找不到playerdata文件夹")
        return []

    players = []
    for file in os.listdir(playerdata_dir):
        if file.endswith('.dat') and not file.endswith('.dat_old'):
            uuid = file[:-4]  # 移除.dat后缀
            player_name = None

            # 尝试读取玩家名称
            try:
                player_file = os.path.join(playerdata_dir, file)

                # 尝试使用nbtlib
                try:
                    from nbtlib import nbt
                    with open(player_file, 'rb') as f:
                        player_data = nbt.load(f)
                    player_name = get_player_name_from_nbt(player_data)
                except ImportError:
                    pass

                # 如果nbtlib失败，尝试anvil
                if not player_name:
                    try:
                        import anvil
                        player_data = anvil.NBTFile.load(player_file)
                        player_name = get_player_name_from_nbt(player_data)
                    except ImportError:
                        pass

            except Exception as e:
                pass  # 忽略读取错误

            players.append((uuid, player_name))

    return players

def main():
    # 解析命令行参数
    if len(sys.argv) < 2:
        print("用法: python transfer_playerdata.py <UUID> [存档路径]")
        print("\n可用的玩家:")

        # 尝试从当前目录列出玩家
        current_dir = os.getcwd()
        players = list_available_players(current_dir)
        if players:
            for uuid, name in players:
                if name:
                    print(f"  {uuid} ({name})")
                else:
                    print(f"  {uuid}")
        else:
            print("  未找到玩家数据文件")

        sys.exit(1)
    
    uuid = sys.argv[1]
    save_path = sys.argv[2] if len(sys.argv) > 2 else os.getcwd()
    
    # 验证存档路径
    if not os.path.exists(save_path):
        print(f"错误：存档路径不存在: {save_path}")
        sys.exit(1)
    
    print(f"UUID: {uuid}")
    print(f"存档路径: {save_path}")
    print("-" * 50)
    
    # 列出可用的玩家
    players = list_available_players(save_path)
    player_uuids = [uuid for uuid, name in players]

    if uuid not in player_uuids:
        print(f"警告：UUID {uuid} 不在可用玩家列表中")
        print("可用的玩家:")
        for player_uuid, name in players:
            if name:
                print(f"  {player_uuid} ({name})")
            else:
                print(f"  {player_uuid}")

        response = input("是否继续？(y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            sys.exit(0)
    
    # 尝试使用不同的NBT库
    success = False
    
    # 首先尝试nbtlib
    try:
        from nbtlib import nbt
        success = transfer_playerdata_nbtlib(uuid, save_path)
    except ImportError:
        pass
    
    # 如果nbtlib失败，尝试anvil
    if not success:
        try:
            import anvil
            success = transfer_playerdata_anvil(uuid, save_path)
        except ImportError:
            pass
    
    if not success:
        print("错误：无法完成数据转移")
        sys.exit(1)

if __name__ == "__main__":
    main()
