Minecraft 玩家数据转移工具 - 使用指南
==========================================

问题说明：
---------
在Minecraft中，单人模式使用level.dat中的玩家数据，而多人模式使用playerdata文件夹中的UUID对应文件。
当您要将存档移交给其他人时，需要将指定玩家的数据从playerdata复制到level.dat中。

解决方案：
---------
本工具提供了多种方式来解决这个问题：

1. 快速使用（推荐）
   - 双击运行"快速转移.bat"
   - 按照提示选择玩家
   - 自动完成转移

2. 测试工具
   - 运行"python test_tools.py"检查环境
   - 确保所有组件正常工作

3. 手动使用
   - 运行"python simple_transfer.py"
   - 或"python transfer_playerdata.py <UUID>"

使用步骤：
---------
1. 关闭Minecraft游戏
2. 将所有脚本文件复制到存档文件夹中
3. 双击运行"快速转移.bat"
4. 从列表中选择要转移的玩家
5. 等待转移完成
6. 启动Minecraft测试

当前存档中的玩家UUID：
--------------------
07ae91b0-3077-420b-bb8c-f29041d05cd9
4181f174-88b9-453d-980f-686f3d521f16
749126bc-4467-41b4-be12-d24f4496cfad
90a2ba02-6174-4ed2-a3cc-11adc6a0ba4c
ae51ed3d-e647-4719-9d3d-f2284d3c556d
b9947d90-4005-4d4e-b425-9fa5513b5d75
ececee07-d1c5-45b5-8981-2cd403614dfe
f6e77564-05e1-424b-8803-8f124f7f588d
ff75ac3f-d688-4d0a-8d5d-6a29e3545dd8

安全提示：
---------
- 工具会自动创建level.dat.backup备份文件
- 建议手动备份整个存档文件夹
- 如果出现问题，可以从备份恢复

故障排除：
---------
1. 如果提示"找不到Python"：
   - 下载安装Python: https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. 如果提示NBT库错误：
   - 运行: pip install nbtlib
   - 或运行: pip install anvil-parser

3. 如果转移失败：
   - 检查文件权限
   - 确保Minecraft已关闭
   - 尝试以管理员身份运行

4. 恢复备份：
   - 将level.dat.backup重命名为level.dat

联系支持：
---------
如果遇到问题，请检查：
1. 是否在正确的存档目录中运行
2. 是否已关闭Minecraft
3. 是否有足够的文件权限
4. Python环境是否正确安装

文件说明：
---------
快速转移.bat        - 一键运行工具（推荐）
simple_transfer.py  - 简化版脚本
transfer_playerdata.py - 完整版脚本
test_tools.py       - 环境测试工具
install_dependencies.bat - 依赖安装
README.md          - 详细说明文档
使用指南.txt        - 本文件
