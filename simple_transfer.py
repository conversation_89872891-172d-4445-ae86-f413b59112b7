#!/usr/bin/env python3
"""
简化版Minecraft玩家数据转移脚本
将指定UUID的playerdata数据载入level.dat中

使用方法:
python simple_transfer.py
"""

import os
import sys
import shutil
import gzip
import struct

def read_nbt_file(file_path):
    """简单的NBT文件读取器"""
    try:
        with open(file_path, 'rb') as f:
            # 检查是否是gzip压缩的
            magic = f.read(2)
            f.seek(0)
            
            if magic == b'\x1f\x8b':  # gzip magic number
                with gzip.open(file_path, 'rb') as gz_f:
                    return gz_f.read()
            else:
                return f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def write_nbt_file(file_path, data):
    """简单的NBT文件写入器"""
    try:
        # 检查原文件是否是gzip压缩的
        with open(file_path, 'rb') as f:
            magic = f.read(2)
        
        if magic == b'\x1f\x8b':  # gzip压缩
            with gzip.open(file_path, 'wb') as gz_f:
                gz_f.write(data)
        else:
            with open(file_path, 'wb') as f:
                f.write(data)
        return True
    except Exception as e:
        print(f"写入文件失败: {e}")
        return False

def backup_file(file_path):
    """创建文件备份"""
    backup_path = f"{file_path}.backup"
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"创建备份失败: {e}")
        return None

def list_players():
    """列出可用的玩家UUID"""
    playerdata_dir = "playerdata"
    if not os.path.exists(playerdata_dir):
        print("错误：找不到playerdata文件夹")
        return []
    
    players = []
    print("可用的玩家UUID:")
    print("-" * 40)
    
    for i, file in enumerate(os.listdir(playerdata_dir), 1):
        if file.endswith('.dat') and not file.endswith('.dat_old'):
            uuid = file[:-4]  # 移除.dat后缀
            players.append(uuid)
            print(f"{i}. {uuid}")
    
    return players

def simple_transfer(source_uuid):
    """简单的数据转移方法"""
    playerdata_path = os.path.join("playerdata", f"{source_uuid}.dat")
    level_dat_path = "level.dat"
    
    # 检查文件是否存在
    if not os.path.exists(playerdata_path):
        print(f"错误：找不到玩家数据文件: {playerdata_path}")
        return False
    
    if not os.path.exists(level_dat_path):
        print(f"错误：找不到level.dat文件: {level_dat_path}")
        return False
    
    print(f"正在读取玩家数据: {playerdata_path}")
    player_data = read_nbt_file(playerdata_path)
    if not player_data:
        return False
    
    print(f"正在读取level.dat: {level_dat_path}")
    level_data = read_nbt_file(level_dat_path)
    if not level_data:
        return False
    
    # 创建备份
    if not backup_file(level_dat_path):
        print("警告：无法创建备份，是否继续？(y/N)")
        if input().lower() != 'y':
            return False
    
    print("正在尝试使用NBT库进行数据转移...")
    
    # 尝试使用nbtlib
    try:
        from nbtlib import nbt
        return transfer_with_nbtlib(source_uuid)
    except ImportError:
        print("nbtlib未安装，尝试anvil库...")
    
    # 尝试使用anvil
    try:
        import anvil
        return transfer_with_anvil(source_uuid)
    except ImportError:
        print("anvil库未安装，尝试手动方法...")
    
    # 手动方法（不推荐，但作为最后手段）
    print("警告：将使用简单的二进制复制方法")
    print("这种方法可能不完全准确，建议安装NBT处理库")
    response = input("是否继续？(y/N): ")
    if response.lower() != 'y':
        return False
    
    return manual_transfer(player_data, level_data, level_dat_path)

def transfer_with_nbtlib(source_uuid):
    """使用nbtlib进行转移"""
    try:
        from nbtlib import nbt
        
        playerdata_path = os.path.join("playerdata", f"{source_uuid}.dat")
        level_dat_path = "level.dat"
        
        # 读取数据
        with open(playerdata_path, 'rb') as f:
            player_data = nbt.load(f)
        
        with open(level_dat_path, 'rb') as f:
            level_data = nbt.load(f)
        
        # 转移数据
        level_data['Data']['Player'] = player_data.copy()
        
        # 保存
        with open(level_dat_path, 'wb') as f:
            level_data.save(f)
        
        print("✓ 使用nbtlib成功转移数据！")
        return True
        
    except Exception as e:
        print(f"nbtlib转移失败: {e}")
        return False

def transfer_with_anvil(source_uuid):
    """使用anvil进行转移"""
    try:
        import anvil
        
        playerdata_path = os.path.join("playerdata", f"{source_uuid}.dat")
        level_dat_path = "level.dat"
        
        # 读取数据
        player_data = anvil.NBTFile.load(playerdata_path)
        level_data = anvil.NBTFile.load(level_dat_path)
        
        # 转移数据
        level_data['Data']['Player'] = player_data.copy()
        
        # 保存
        level_data.save(level_dat_path)
        
        print("✓ 使用anvil成功转移数据！")
        return True
        
    except Exception as e:
        print(f"anvil转移失败: {e}")
        return False

def manual_transfer(player_data, level_data, level_dat_path):
    """手动转移方法（不推荐）"""
    try:
        # 这是一个非常简化的方法，可能不完全准确
        # 建议用户安装proper NBT库
        print("警告：手动方法可能导致数据损坏")
        print("强烈建议安装nbtlib或anvil库")
        
        # 简单地将玩家数据附加到level.dat
        # 这不是正确的NBT合并方式，但可能在某些情况下有效
        
        backup_path = f"{level_dat_path}.manual_backup"
        shutil.copy2(level_dat_path, backup_path)
        
        # 创建一个包含两个文件内容的新文件
        # 注意：这不是正确的NBT格式！
        combined_data = level_data + b'\n--- PLAYER DATA ---\n' + player_data
        
        if write_nbt_file(level_dat_path, combined_data):
            print("⚠ 手动转移完成，但可能不稳定")
            print(f"如果出现问题，请从备份恢复: {backup_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"手动转移失败: {e}")
        return False

def main():
    print("=== Minecraft 玩家数据转移工具 ===")
    print()
    
    # 检查当前目录
    if not os.path.exists("level.dat"):
        print("错误：当前目录不是Minecraft存档目录")
        print("请在存档文件夹中运行此脚本")
        input("按回车键退出...")
        return
    
    # 列出可用玩家
    players = list_players()
    if not players:
        print("未找到任何玩家数据")
        input("按回车键退出...")
        return
    
    print()
    print("请选择要转移的玩家:")
    print("输入数字选择，或直接输入完整UUID")
    
    choice = input("选择: ").strip()
    
    selected_uuid = None
    
    # 检查是否是数字选择
    try:
        index = int(choice) - 1
        if 0 <= index < len(players):
            selected_uuid = players[index]
        else:
            print("无效的选择")
            input("按回车键退出...")
            return
    except ValueError:
        # 不是数字，检查是否是有效的UUID
        if choice in players:
            selected_uuid = choice
        else:
            print("无效的UUID")
            input("按回车键退出...")
            return
    
    print(f"\n选择的玩家UUID: {selected_uuid}")
    print("=" * 50)
    
    # 执行转移
    if simple_transfer(selected_uuid):
        print("\n✓ 数据转移完成！")
        print("现在这个存档可以作为单人存档使用了")
    else:
        print("\n✗ 数据转移失败")
        print("请检查错误信息并重试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
